package service

import (
	"sync"

	"web3-control/internal/config"
	"web3-control/internal/database"
	"web3-control/internal/models/entity"

	"gorm.io/gorm"
)

// Service 服务层结构体
type Service struct {
	cfg     *config.Config
	db      *gorm.DB
	clients sync.Map // 用于存储在线客户端
}

// NewService 创建新的服务实例
func NewService(cfg *config.Config) *Service {
	return &Service{
		cfg: cfg,
		db:  database.GetDB(),
	}
}

// AddClient 添加新的客户端
func (s *Service) AddClient(client *entity.Client) error {
	// 保存到数据库
	if err := s.db.Create(client).Error; err != nil {
		return err
	}
	// 添加到在线客户端列表
	s.clients.Store(client.ClientID, client)
	return nil
}

// RemoveClient 移除客户端
func (s *Service) RemoveClient(clientID string) error {
	// 从数据库中删除
	if err := s.db.Delete(&entity.Client{}, "client_id = ?", clientID).Error; err != nil {
		return err
	}
	// 从在线客户端列表中删除
	s.clients.Delete(clientID)
	return nil
}

// GetClient 获取客户端信息
func (s *Service) GetClient(clientID string) (*entity.Client, error) {
	var client entity.Client
	if err := s.db.First(&client, "client_id = ?", clientID).Error; err != nil {
		return nil, err
	}
	return &client, nil
}

// ListClients 获取所有客户端列表
func (s *Service) ListClients() ([]*entity.Client, error) {
	var clients []*entity.Client
	if err := s.db.Find(&clients).Error; err != nil {
		return nil, err
	}
	return clients, nil
}

// ListClientsByGroup 获取指定分组的所有客户端
func (s *Service) ListClientsByGroup(group string) ([]*entity.Client, error) {
	var clients []*entity.Client
	if err := s.db.Where("group = ?", group).Find(&clients).Error; err != nil {
		return nil, err
	}
	return clients, nil
}

// UpdateClient 更新客户端信息
func (s *Service) UpdateClient(client *entity.Client) error {
	return s.db.Save(client).Error
}

// ValidateAuth 验证认证密钥
func (s *Service) ValidateAuth(auth string) bool {
	return s.cfg.Server.Auth == auth
}

// AddRecording 添加录制
func (s *Service) AddRecording(recording *entity.Recording) error {
	return s.db.Create(recording).Error
}

// GetRecording 获取录制信息
func (s *Service) GetRecording(id uint) (*entity.Recording, error) {
	var recording entity.Recording
	if err := s.db.Preload("Events").First(&recording, id).Error; err != nil {
		return nil, err
	}
	return &recording, nil
}

// ListRecordings 获取所有录制列表
func (s *Service) ListRecordings() ([]*entity.Recording, error) {
	var recordings []*entity.Recording
	if err := s.db.Find(&recordings).Error; err != nil {
		return nil, err
	}
	return recordings, nil
}

// DeleteRecording 删除录制
func (s *Service) DeleteRecording(id uint) error {
	return s.db.Delete(&entity.Recording{}, id).Error
}

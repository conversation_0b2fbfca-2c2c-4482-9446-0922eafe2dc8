package service

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"web3-control/internal/models"
	"web3-control/internal/models/entity"
)

// RecordingManager 录制管理器
type RecordingManager struct {
	isRecording   bool
	currentRecord *entity.Recording
	events        []entity.Event
	mutex         sync.RWMutex
	svc           *Service
}

// NewRecordingManager 创建录制管理器
func NewRecordingManager(svc *Service) *RecordingManager {
	return &RecordingManager{
		svc: svc,
	}
}

// StartRecording 开始录制
func (rm *RecordingManager) StartRecording(name, description string) error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	if rm.isRecording {
		return fmt.Errorf("录制已在进行中")
	}

	// 创建新的录制记录
	recording := &entity.Recording{
		Name:        name,
		Description: description,
	}

	// 保存到数据库
	if err := rm.svc.AddRecording(recording); err != nil {
		return fmt.Errorf("创建录制记录失败: %v", err)
	}

	rm.currentRecord = recording
	rm.events = make([]entity.Event, 0)
	rm.isRecording = true

	return nil
}

// StopRecording 停止录制
func (rm *RecordingManager) StopRecording() error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	if !rm.isRecording {
		return fmt.Errorf("当前没有进行录制")
	}

	// 保存所有事件到数据库
	for _, event := range rm.events {
		event.RecordingID = rm.currentRecord.ID
		if err := rm.svc.db.Create(&event).Error; err != nil {
			return fmt.Errorf("保存事件失败: %v", err)
		}
	}

	rm.isRecording = false
	rm.currentRecord = nil
	rm.events = nil

	return nil
}

// AddEvent 添加事件到当前录制
func (rm *RecordingManager) AddEvent(eventType string, data map[string]interface{}) error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	if !rm.isRecording {
		return nil // 如果没有在录制，忽略事件
	}

	// 将事件数据序列化为JSON
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化事件数据失败: %v", err)
	}

	event := entity.Event{
		Type:      eventType,
		Data:      string(dataBytes),
		Timestamp: time.Now(),
	}

	rm.events = append(rm.events, event)
	return nil
}

// IsRecording 检查是否正在录制
func (rm *RecordingManager) IsRecording() bool {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	return rm.isRecording
}

// GetCurrentRecording 获取当前录制信息
func (rm *RecordingManager) GetCurrentRecording() *entity.Recording {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	return rm.currentRecord
}

// PlayRecording 播放录制
func (rm *RecordingManager) PlayRecording(recordingID uint, targetGroup string, targetClients []string) error {
	// 获取录制信息
	recording, err := rm.svc.GetRecording(recordingID)
	if err != nil {
		return fmt.Errorf("获取录制信息失败: %v", err)
	}

	// 在新的goroutine中播放录制
	go rm.playRecordingAsync(recording, targetGroup, targetClients)

	return nil
}

// playRecordingAsync 异步播放录制
func (rm *RecordingManager) playRecordingAsync(recording *entity.Recording, targetGroup string, targetClients []string) {
	if len(recording.Events) == 0 {
		return
	}

	// 按时间戳排序事件
	events := recording.Events
	
	// 计算第一个事件的时间作为基准
	baseTime := events[0].Timestamp

	for i, event := range events {
		// 计算延迟时间
		var delay time.Duration
		if i > 0 {
			delay = event.Timestamp.Sub(events[i-1].Timestamp)
		}

		// 等待延迟时间
		if delay > 0 {
			time.Sleep(delay)
		}

		// 解析事件数据
		var eventData map[string]interface{}
		if err := json.Unmarshal([]byte(event.Data), &eventData); err != nil {
			continue
		}

		// 创建WebSocket消息
		wsMsg := models.WebSocketMessage{
			Type:      "event",
			Timestamp: time.Now().Unix(),
			Data: models.Event{
				Type:      event.Type,
				Timestamp: event.Timestamp.Unix(),
				Data:      eventData,
			},
		}

		// 序列化消息
		msgBytes, err := json.Marshal(wsMsg)
		if err != nil {
			continue
		}

		// 发送到目标客户端
		if targetGroup != "" {
			// 发送到指定分组
			rm.broadcastToGroup(targetGroup, msgBytes)
		} else if len(targetClients) > 0 {
			// 发送到指定客户端
			for _, clientID := range targetClients {
				rm.sendToClient(clientID, msgBytes)
			}
		} else {
			// 发送到所有从控客户端
			rm.broadcastToSlaves(msgBytes)
		}
	}
}

// broadcastToGroup 向指定分组广播消息
func (rm *RecordingManager) broadcastToGroup(group string, message []byte) {
	// 这里需要访问连接管理器，暂时留空
	// TODO: 实现分组广播
}

// sendToClient 向指定客户端发送消息
func (rm *RecordingManager) sendToClient(clientID string, message []byte) {
	// 这里需要访问连接管理器，暂时留空
	// TODO: 实现客户端发送
}

// broadcastToSlaves 向所有从控客户端广播消息
func (rm *RecordingManager) broadcastToSlaves(message []byte) {
	// 这里需要访问连接管理器，暂时留空
	// TODO: 实现从控广播
}

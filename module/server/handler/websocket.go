package handler

import (
	"encoding/json"
	"log"
	"net/http"
	"sync"
	"time"

	"web3-control/internal/models"
	"web3-control/internal/models/entity"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

// WebSocket升级器
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 允许跨域连接
	},
}

// ClientConnection 表示一个客户端连接
type ClientConnection struct {
	ClientID   string          `json:"client_id"`
	Name       string          `json:"name"`
	Group      string          `json:"group"`
	IsMaster   bool            `json:"is_master"`
	Conn       *websocket.Conn `json:"-"`
	LastPing   time.Time       `json:"last_ping"`
	IsOnline   bool            `json:"is_online"`
	mutex      sync.RWMutex    `json:"-"`
}

// ConnectionManager 连接管理器
type ConnectionManager struct {
	clients    sync.Map // map[string]*ClientConnection
	broadcast  chan []byte
	register   chan *ClientConnection
	unregister chan *ClientConnection
}

// NewConnectionManager 创建新的连接管理器
func NewConnectionManager() *ConnectionManager {
	return &ConnectionManager{
		broadcast:  make(chan []byte),
		register:   make(chan *ClientConnection),
		unregister: make(chan *ClientConnection),
	}
}

// Run 运行连接管理器
func (cm *ConnectionManager) Run() {
	for {
		select {
		case client := <-cm.register:
			cm.clients.Store(client.ClientID, client)
			log.Printf("客户端 %s (%s) 已连接", client.ClientID, client.Name)

		case client := <-cm.unregister:
			if _, ok := cm.clients.Load(client.ClientID); ok {
				cm.clients.Delete(client.ClientID)
				close(client.Conn.WriteMessage)
				log.Printf("客户端 %s (%s) 已断开连接", client.ClientID, client.Name)
			}

		case message := <-cm.broadcast:
			cm.clients.Range(func(key, value interface{}) bool {
				client := value.(*ClientConnection)
				select {
				case client.Conn.WriteMessage(websocket.TextMessage, message):
				default:
					cm.clients.Delete(key)
					close(client.Conn.WriteMessage)
				}
				return true
			})
		}
	}
}

// BroadcastToGroup 向指定分组广播消息
func (cm *ConnectionManager) BroadcastToGroup(group string, message []byte) {
	cm.clients.Range(func(key, value interface{}) bool {
		client := value.(*ClientConnection)
		if client.Group == group {
			client.mutex.Lock()
			err := client.Conn.WriteMessage(websocket.TextMessage, message)
			client.mutex.Unlock()
			if err != nil {
				log.Printf("向客户端 %s 发送消息失败: %v", client.ClientID, err)
				cm.unregister <- client
			}
		}
		return true
	})
}

// BroadcastToSlaves 向所有从控客户端广播消息
func (cm *ConnectionManager) BroadcastToSlaves(message []byte) {
	cm.clients.Range(func(key, value interface{}) bool {
		client := value.(*ClientConnection)
		if !client.IsMaster {
			client.mutex.Lock()
			err := client.Conn.WriteMessage(websocket.TextMessage, message)
			client.mutex.Unlock()
			if err != nil {
				log.Printf("向从控客户端 %s 发送消息失败: %v", client.ClientID, err)
				cm.unregister <- client
			}
		}
		return true
	})
}

// SendToClient 向指定客户端发送消息
func (cm *ConnectionManager) SendToClient(clientID string, message []byte) error {
	if value, ok := cm.clients.Load(clientID); ok {
		client := value.(*ClientConnection)
		client.mutex.Lock()
		defer client.mutex.Unlock()
		return client.Conn.WriteMessage(websocket.TextMessage, message)
	}
	return nil
}

// GetOnlineClients 获取在线客户端列表
func (cm *ConnectionManager) GetOnlineClients() []*ClientConnection {
	var clients []*ClientConnection
	cm.clients.Range(func(key, value interface{}) bool {
		client := value.(*ClientConnection)
		clients = append(clients, client)
		return true
	})
	return clients
}

// 全局连接管理器实例
var connManager = NewConnectionManager()

// init 初始化连接管理器
func init() {
	go connManager.Run()
}

// HandleWebSocket 处理WebSocket连接
func (h *Handler) HandleWebSocket(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}
	defer conn.Close()

	// 等待客户端认证
	var authMsg models.WebSocketMessage
	err = conn.ReadJSON(&authMsg)
	if err != nil {
		log.Printf("读取认证消息失败: %v", err)
		return
	}

	if authMsg.Type != "auth" {
		log.Printf("期望认证消息，但收到: %s", authMsg.Type)
		return
	}

	// 解析认证数据
	var authData models.ClientRegisterRequest
	authBytes, _ := json.Marshal(authMsg.Data)
	err = json.Unmarshal(authBytes, &authData)
	if err != nil {
		log.Printf("解析认证数据失败: %v", err)
		return
	}

	// 验证认证信息
	if !h.svc.ValidateAuth(authData.AuthToken) {
		response := models.ClientRegisterResponse{
			Code:    401,
			Message: "认证失败",
		}
		conn.WriteJSON(response)
		return
	}

	// 创建客户端连接
	client := &ClientConnection{
		ClientID: authData.ClientID,
		Name:     authData.Name,
		Group:    authData.Group,
		IsMaster: authData.IsMaster,
		Conn:     conn,
		LastPing: time.Now(),
		IsOnline: true,
	}

	// 保存客户端信息到数据库
	dbClient := &entity.Client{
		ClientID: authData.ClientID,
		Name:     authData.Name,
		Group:    authData.Group,
		IsMaster: authData.IsMaster,
		Online:   1,
	}

	// 检查客户端是否已存在
	existingClient, err := h.svc.GetClient(authData.ClientID)
	if err != nil {
		// 客户端不存在，创建新的
		err = h.svc.AddClient(dbClient)
		if err != nil {
			log.Printf("保存客户端信息失败: %v", err)
		}
	} else {
		// 客户端已存在，更新在线状态
		existingClient.Online = 1
		existingClient.Name = authData.Name
		existingClient.Group = authData.Group
		existingClient.IsMaster = authData.IsMaster
		now := time.Now()
		existingClient.LastOnline = &now
		h.svc.UpdateClient(existingClient)
	}

	// 注册客户端连接
	connManager.register <- client

	// 发送认证成功响应
	response := models.ClientRegisterResponse{
		Code:    0,
		Message: "认证成功",
		Data: struct {
			ClientID string `json:"client_id"`
			Online   bool   `json:"online"`
		}{
			ClientID: authData.ClientID,
			Online:   true,
		},
	}
	conn.WriteJSON(response)

	// 处理客户端消息
	h.handleClientMessages(client)
}

// handleClientMessages 处理客户端消息
func (h *Handler) handleClientMessages(client *ClientConnection) {
	defer func() {
		connManager.unregister <- client
		// 更新数据库中的离线状态
		if dbClient, err := h.svc.GetClient(client.ClientID); err == nil {
			dbClient.Online = 0
			now := time.Now()
			dbClient.LastOnline = &now
			h.svc.UpdateClient(dbClient)
		}
	}()

	for {
		var msg models.WebSocketMessage
		err := client.Conn.ReadJSON(&msg)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket错误: %v", err)
			}
			break
		}

		// 更新最后ping时间
		client.LastPing = time.Now()

		// 处理不同类型的消息
		switch msg.Type {
		case "heartbeat":
			// 心跳响应
			response := models.WebSocketMessage{
				Type:      "heartbeat",
				Timestamp: time.Now().Unix(),
				Data:      "pong",
			}
			client.Conn.WriteJSON(response)

		case "event":
			// 事件消息，如果是主控客户端发送的，则广播给从控客户端
			if client.IsMaster {
				eventBytes, _ := json.Marshal(msg)
				connManager.BroadcastToSlaves(eventBytes)
			}

		case "status":
			// 状态更新消息
			log.Printf("收到客户端 %s 的状态更新: %v", client.ClientID, msg.Data)

		default:
			log.Printf("未知消息类型: %s", msg.Type)
		}
	}
}

// GetOnlineClients 获取在线客户端列表API
func (h *Handler) GetOnlineClients(c *gin.Context) {
	clients := connManager.GetOnlineClients()
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    clients,
	})
}

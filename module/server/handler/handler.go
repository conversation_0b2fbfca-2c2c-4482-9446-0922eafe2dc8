package handler

import (
	"net/http"
	"strconv"

	"web3-control/module/server/service"

	"github.com/gin-gonic/gin"
)

// Handler HTTP处理器
type Handler struct {
	svc *service.Service
}

// NewHandler 创建新的处理器
func NewHandler(svc *service.Service) *Handler {
	return &Handler{svc: svc}
}

// RegisterRoutes 注册路由
func RegisterRoutes(r *gin.Engine, svc *service.Service) {
	h := NewHandler(svc)

	// WebSocket路由
	r.GET("/ws", h.HandleWebSocket)

	// API路由组
	api := r.Group("/api")
	{
		// 客户端管理
		api.GET("/clients", h.ListClients)
		api.GET("/clients/online", h.GetOnlineClients)
		api.GET("/clients/:id", h.GetClient)
		api.GET("/clients/group/:group", h.ListClientsByGroup)
		api.DELETE("/clients/:id", h.RemoveClient)

		// 录制管理
		api.GET("/recordings", h.ListRecordings)
		api.GET("/recordings/:id", h.GetRecording)
		api.POST("/recording/start", h.StartRecording)
		api.POST("/recording/stop", h.StopRecording)
		api.POST("/recording/play", h.PlayRecording)
		api.DELETE("/recordings/:id", h.DeleteRecording)
	}

	// Web控制台
	r.Static("/static", "./web/static")
	r.LoadHTMLGlob("web/template/*")
	r.GET("/", h.ServeWebConsole)
}

// ListClients 获取所有客户端列表
func (h *Handler) ListClients(c *gin.Context) {
	clients, err := h.svc.ListClients()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    clients,
	})
}

// GetClient 获取指定客户端信息
func (h *Handler) GetClient(c *gin.Context) {
	id := c.Param("id")
	client, err := h.svc.GetClient(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "client not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    client,
	})
}

// ListClientsByGroup 获取指定分组的所有客户端
func (h *Handler) ListClientsByGroup(c *gin.Context) {
	group := c.Param("group")
	clients, err := h.svc.ListClientsByGroup(group)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    clients,
	})
}

// RemoveClient 移除客户端
func (h *Handler) RemoveClient(c *gin.Context) {
	id := c.Param("id")
	if err := h.svc.RemoveClient(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
	})
}

// ListRecordings 获取所有录制列表
func (h *Handler) ListRecordings(c *gin.Context) {
	recordings, err := h.svc.ListRecordings()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    recordings,
	})
}

// GetRecording 获取指定录制信息
func (h *Handler) GetRecording(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "invalid recording id",
		})
		return
	}

	recording, err := h.svc.GetRecording(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "recording not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    recording,
	})
}

// StartRecording 开始录制
func (h *Handler) StartRecording(c *gin.Context) {
	// TODO: 实现录制功能
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
	})
}

// StopRecording 停止录制
func (h *Handler) StopRecording(c *gin.Context) {
	// TODO: 实现录制功能
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
	})
}

// PlayRecording 播放录制
func (h *Handler) PlayRecording(c *gin.Context) {
	var req struct {
		RecordingID uint   `json:"recording_id" binding:"required"`
		Group       string `json:"group"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	// TODO: 实现播放功能
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
	})
}

// DeleteRecording 删除录制
func (h *Handler) DeleteRecording(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "invalid recording id",
		})
		return
	}

	if err := h.svc.DeleteRecording(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
	})
}

// ServeWebConsole 提供Web控制台页面
func (h *Handler) ServeWebConsole(c *gin.Context) {
	c.HTML(http.StatusOK, "index.html", gin.H{
		"title": "浏览器群控系统",
	})
}

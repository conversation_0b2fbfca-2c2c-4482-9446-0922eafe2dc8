package database

import (
	"log"
	"time"
	"web3-control/internal/config"
	"web3-control/internal/models/entity"

	"gorm.io/gorm/logger"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var db *gorm.DB

// InitDB 初始化数据库连接
func InitDB(cfg *config.Config) error {
	// 配置GORM日志
	gormLogger := logger.New(
		log.New(log.Writer(), "\r\n[GORM] ", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  true,
		},
	)

	var err error
	db, err = gorm.Open(mysql.Open(cfg.GetDSN()), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		return err
	}

	// 自动迁移数据库表结构
	if err := AutoMigrate(db); err != nil {
		log.Printf("数据库迁移失败: %v", err)
		return err
	}
	log.Println("数据库连接成功")
	return nil
}

// AutoMigrate 自动迁移数据库表结构
func AutoMigrate(db *gorm.DB) error {
	// 迁移所有表
	err := db.AutoMigrate(
		&entity.Client{},
		&entity.Browser{},
		&entity.BrowserStatus{},
		&entity.Proxy{},
		&entity.ProxyUsage{},
		&entity.Recording{},
		&entity.Event{},
	)
	if err != nil {
		return err
	}

	log.Printf("数据库表结构迁移完成")
	return nil
}

// GetDB 获取数据库连接
func GetDB() *gorm.DB {
	return db
}

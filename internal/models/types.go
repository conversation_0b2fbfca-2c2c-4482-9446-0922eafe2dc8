package models

import (
	"time"

	"github.com/gorilla/websocket"
)

// Event represents an event data structure
type Event struct {
	Type      string                 `json:"type"`
	Timestamp int64                  `json:"timestamp,omitempty"`
	Delay     int                    `json:"delay,omitempty"`
	Data      map[string]interface{} `json:"data"`
}

// RecordingSession represents a recording session
type RecordingSession struct {
	Name      string    `json:"name"`
	CreatedAt time.Time `json:"created_at"`
	Events    []Event   `json:"events"`
}

// Client represents a connected client device
type Client struct {
	ID       string
	Name     string
	Group    string
	Conn     *websocket.Conn
	IsMaster bool
}

// RegisterRequest represents a client registration request
type RegisterRequest struct {
	DeviceID   string `json:"device_id"`
	AuthToken  string `json:"auth_token"`
	DeviceName string `json:"device_name"`
	Group      string `json:"group"`
	IsMaster   bool   `json:"is_master"`
}

// WebSocketMessage represents a WebSocket message structure
type WebSocketMessage struct {
	Type      string      `json:"type"`      // 消息类型
	Timestamp int64       `json:"timestamp"` // 时间戳
	Data      interface{} `json:"data"`      // 消息数据
}

// ClientRegisterRequest 表示客户端注册请求
type ClientRegisterRequest struct {
	ClientID  string `json:"client_id"`  // 客户端唯一标识
	AuthToken string `json:"auth_token"` // 认证令牌
	Name      string `json:"name"`       // 客户端名称
	Group     string `json:"group"`      // 分组名称
	IsMaster  bool   `json:"is_master"`  // 是否主控
}

// ClientRegisterResponse 表示客户端注册响应
type ClientRegisterResponse struct {
	Code    int    `json:"code"`    // 状态码 0:成功 其他:失败
	Message string `json:"message"` // 响应消息
	Data    struct {
		ClientID string `json:"client_id"`
		Online   bool   `json:"online"`
	} `json:"data"`
}

package entity

import (
	"gorm.io/gorm"
	"time"
)

// Recording represents a recording session in the database
type Recording struct {
	ID          uint           `gorm:"primaryKey" json:"id"`
	Name        string         `gorm:"column:name;type:varchar(100);not null;comment:录制名称" json:"name"`
	Description string         `gorm:"column:description;type:varchar(500);comment:录制描述" json:"description"`
	Events      []Event        `gorm:"foreignKey:RecordingID" json:"events"`
	CreatedAt   time.Time      `gorm:"column:created_at;autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt   time.Time      `gorm:"column:updated_at;autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName returns the table name
func (Recording) TableName() string {
	return "t_recording"
}

// Event represents an event in a recording session
type Event struct {
	ID          uint           `gorm:"primaryKey" json:"id"`
	RecordingID uint           `gorm:"column:recording_id;not null;comment:录制ID" json:"recording_id"`
	Type        string         `gorm:"column:type;type:varchar(50);not null;comment:事件类型" json:"type"`
	Data        string         `gorm:"column:data;type:text;not null;comment:事件数据" json:"data"`
	Timestamp   time.Time      `gorm:"column:timestamp;comment:事件时间戳" json:"timestamp"`
	CreatedAt   time.Time      `gorm:"column:created_at;autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt   time.Time      `gorm:"column:updated_at;autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName returns the table name
func (Event) TableName() string {
	return "t_event"
}

# 浏览器群控系统架构设计

## 系统概述

本系统是一个基于 Chrome DevTools Protocol (CDP) 的浏览器群控平台，支持一个主控浏览器的操作同步到多个从控浏览器（可跨设备），并具备事件录制、重放和安全随机化功能。

## 核心架构

### 三层架构模式
```
Web控制台 <--HTTP/WebSocket--> 服务端 <--WebSocket--> 客户端 <--CDP--> Chrome浏览器
```

### 技术栈总览
- **后端服务**: Go 1.23+ + Gin + GORM + Gorilla WebSocket
- **数据库**: MySQL 8.0+
- **前端**: HTML5 + Bootstrap 5 + Vanilla JavaScript
- **浏览器控制**: Chrome DevTools Protocol (CDP)
- **通信协议**: WebSocket + HTTP REST API
- **配置管理**: YAML
- **依赖管理**: Go Modules

### 项目结构
```
web3-control/
├── cmd/                    # 程序入口点
│   ├── server/            # 服务端主程序
│   └── client/            # 客户端主程序
├── module/                # 业务模块
│   ├── server/           # 服务端业务逻辑
│   │   ├── handler/      # HTTP/WebSocket处理器
│   │   ├── model/        # 数据模型定义
│   │   ├── service/      # 业务服务层
│   │   └── config/       # 服务端配置
│   └── client/           # 客户端业务逻辑
│       ├── browser/      # 浏览器控制模块
│       ├── event/        # 事件处理模块
│       └── noise/        # 随机化处理模块
├── internal/             # 内部共享库
│   ├── protocol/         # 通信协议定义
│   ├── models/           # 数据结构定义
│   ├── config/           # 配置管理
│   ├── database/         # 数据库连接管理
│   └── utils/            # 工具函数
├── web/                  # Web控制台
│   ├── static/           # 静态资源(CSS/JS/图片)
│   └── template/         # HTML模板
├── configs/              # 配置文件
├── scripts/              # 部署和工具脚本
├── docs/                 # 项目文档
└── test/                 # 测试文件
```

## 数据库设计
```sql
CREATE TABLE `t_browser` (
   `id` int NOT NULL AUTO_INCREMENT,
   `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '浏览器多开窗口名称',
   `client_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '绑定的客户端ID',
   `group` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '浏览器多开窗口分组',
   `proxy` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '使用的代理',
   `user_dir` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户资料目录',
   `port` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'CDP调试端口',
   `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
   `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
   `deleted_at` datetime(3) DEFAULT NULL,
   `browser_id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '浏览器多开窗口标识',
   `user_agent` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户代理',
   `window_size` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '窗口大小',
   `headless` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否无头模式',
   `disable_gpu` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否禁用GPU',
   PRIMARY KEY (`id`),
   UNIQUE KEY `idx_t_browser_browser_id` (`browser_id`),
   KEY `idx_t_browser_client_id` (`client_id`),
   KEY `idx_t_browser_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='浏览器窗口配置';

CREATE TABLE `t_browser_status` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `browser_id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '浏览器多开窗口标识',
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '错误信息',
  `url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '当前URL',
  `memory_usage` float(10,2) NOT NULL DEFAULT '0.00' COMMENT '内存使用(MB)',
  `cpu_usage` float(10,2) NOT NULL DEFAULT '0.00' COMMENT 'CPU使用(%)',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_t_browser_status_browser_id` (`browser_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='浏览器状态表';

CREATE TABLE `t_client` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `client_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端ID',
  `is_master` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否主控客户端',
  `name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户端名称',
  `online` tinyint(1) DEFAULT '0' COMMENT '是否在线',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `group` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '分组',
  `last_online` datetime DEFAULT NULL COMMENT '最后在线时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_t_client_client_id` (`client_id`),
  KEY `idx_t_client_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户端信息表';

CREATE TABLE `t_proxy` (
   `id` int unsigned NOT NULL AUTO_INCREMENT,
   `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '代理名称',
   `hosts` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
   `port` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
   `group` longtext COLLATE utf8mb4_unicode_ci,
   `created_at` datetime(3) DEFAULT NULL,
   `username` longtext COLLATE utf8mb4_unicode_ci,
   `password` longtext COLLATE utf8mb4_unicode_ci,
   `status` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT 'healthy',
   `last_used` datetime(3) DEFAULT NULL,
   `success_count` bigint DEFAULT '0',
   `fail_count` bigint DEFAULT '0',
   `updated_at` datetime(3) DEFAULT NULL,
   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理配置表';

CREATE TABLE `t_proxy_usage` (
   `id` bigint unsigned NOT NULL AUTO_INCREMENT,
   `proxy_id` int unsigned NOT NULL COMMENT '代理ID',
   `browser_id` int unsigned NOT NULL COMMENT '浏览器ID',
   `start_time` datetime NOT NULL COMMENT '开始使用时间',
   `end_time` datetime DEFAULT NULL COMMENT '结束使用时间',
   `success` tinyint(1) DEFAULT '0' COMMENT '是否成功',
   `error_msg` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '错误信息',
   PRIMARY KEY (`id`),
   KEY `idx_t_proxy_usage_proxy_id` (`proxy_id`),
   KEY `idx_t_proxy_usage_browser_id` (`browser_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 核心组件

### 1. 服务端 (Golang)

服务端负责：
- 管理客户端连接和认证
- 接收主控浏览器事件并广播到从控浏览器
- 录制事件序列并存储
- 重放事件序列到指定客户端
- 提供 Web 控制台界面
- Web控制台提供完整的客户端和浏览器窗口管理功能：
  - 添加/删除/修改客户端信息
  - 添加/删除/修改浏览器窗口配置，每个浏览器窗口必须绑定到特定客户端，并且每个浏览器都需要绑定代理
  - 支持在Web界面中勾选客户端或浏览器窗口，一键启动所选浏览器
  - 向指定客户端发送启动浏览器的指令
  - 管理代理列表

技术栈：
- Golang
- Gorilla WebSocket
- Gin Web 框架
- Gorm

### 2. 客户端 (Golang)

客户端负责：
- 客户端具有唯一的client_id和认证密钥，通过 WebSocket 连接服务端
- 接收服务端事件并执行
- 控制本地 Chrome 浏览器 (CDP)
- 接收服务端指令并执行，包括：
  - 启动浏览器指令：根据服务端发送的浏览器配置（用户目录、代理设置、调试端口等）启动本地Chrome浏览器
  - 关闭浏览器指令：关闭指定的浏览器窗口
  - 执行操作指令：在浏览器中执行特定操作
- 定期向服务端报告浏览器状态
- 添加随机噪声以模拟人类行为

技术栈：
- Golang
- Chrome DevTools Protocol
- WebSocket 客户端

### 3. 主控浏览器注入脚本 (JavaScript)

注入到主控浏览器的脚本负责：
- 监听用户操作（点击、输入、滚动等）
- 将操作事件实时发送到服务端
- 支持开始/停止录制功能

## 系统流程

### 1. 注册认证流程

```
客户端 ---> 服务端: 发送设备ID和认证密钥
服务端 ---> 客户端: 验证成功/失败响应
```

### 2. 实时同步流程

```
用户 ---> 主控浏览器: 执行操作(点击/输入/滚动)
主控浏览器 ---> 服务端: 发送操作事件
服务端 ---> 从控浏览器: 广播操作事件
从控浏览器: 执行操作(添加随机噪声)
```

### 3. 录制重放流程

```
用户 ---> 服务端: 开始录制
主控浏览器 ---> 服务端: 发送操作事件(服务端保存)
用户 ---> 服务端: 停止录制
用户 ---> 服务端: 选择录制文件重放
服务端 ---> 从控浏览器: 按时间序列发送事件
从控浏览器: 执行操作(添加随机噪声)
```

### 4. 浏览器启动流程

```
用户 ---> Web控制台: 勾选客户端或浏览器窗口
用户 ---> Web控制台: 点击“启动浏览器”按钮
服务端: 查询选中的浏览器窗口配置及其绑定的客户端
服务端 ---> 客户端: 发送启动浏览器指令，包含浏览器配置信息
客户端: 根据配置启动本地Chrome浏览器
客户端 ---> 服务端: 发送浏览器启动状态
服务端 ---> Web控制台: 更新浏览器状态显示
```

## 数据结构

### 1. 事件数据结构

```json
{
  "type": "click|input|scroll",
  "timestamp": 1649123456789,
  "data": {
    // 根据事件类型不同而不同
    "selector": "#login-button",  // 点击/输入事件
    "value": "username",          // 输入事件
    "x": 100, "y": 200            // 滚动事件
  }
}
```

### 2. 客户端注册数据

```json
{
  "device_id": "device-001",
  "auth_token": "secret-token-123",
  "device_name": "测试设备1",
  "group": "group1"
}
```

### 3. 录制文件结构

```json
{
  "name": "登录流程",
  "created_at": "2023-04-08T12:34:56Z",
  "events": [
    {
      "type": "click",
      "timestamp": 0,
      "delay": 0,
      "data": {"selector": "#username"}
    },
    {
      "type": "input",
      "timestamp": 1200,
      "delay": 1200,
      "data": {"selector": "#username", "value": "admin"}
    }
    // ...更多事件
  ]
}
```

### 4. 浏览器启动指令数据结构

```json
{
  "command": "launch_browser",
  "browser_id": "browser-001",
  "config": {
    "name": "浏览器窗口1",
    "user_dir": "C:/Users/<USER>/AppData/Local/Chrome/User Data/Profile1",
    "proxy": "127.0.0.1:8080",
    "port": "9222",
    "additional_args": ["--no-sandbox", "--disable-gpu"]
  }
}
```

### 5. 浏览器状态报告数据结构

```json
{
  "browser_id": "browser-001",
  "status": "running|closed|error",
  "error_message": "启动失败原因...",  // 如果状态为error
  "url": "https://example.com",  // 当前浏览器URL
  "memory_usage": 256.5,  // 内存使用量(MB)
  "cpu_usage": 5.2  // CPU使用率(%)
}
```

## 安全性设计

1. **客户端认证**: 使用设备ID和密钥进行认证
2. **事件噪声**: 每个从控客户端添加随机延迟、位置偏移等噪声
3. **加密通信**: WebSocket 通信使用 TLS 加密
4. **输入模拟**: 模拟人类输入行为，如逐字输入、不规则间隔

## 扩展性设计

1. **分组控制**: 支持将设备分组，针对特定组执行操作
2. **任务调度**: 支持定时执行录制的事件序列
3. **状态监控**: 监控各客户端的在线状态和执行情况
4. **插件系统**: 支持自定义事件处理插件
